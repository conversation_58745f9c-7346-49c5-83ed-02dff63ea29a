package index_stream

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"

	"xiaozhi-esp32-server-golang/constants"
	"xiaozhi-esp32-server-golang/internal/registry/tts"
	"xiaozhi-esp32-server-golang/internal/util"
	log "xiaozhi-esp32-server-golang/logger"

	"github.com/gopxl/beep"
)

// IndexStreamTTSProvider Index Stream TTS 提供者
// 支持流式处理PCM音频数据并编码为Opus格式
type IndexStreamTTSProvider struct {
	APIURL string // TTS API 地址
	Voice  string // 语音角色

	RefAudios string // 少样本音频文件路径
	Seed      int

	AudioFormat string        // 音频格式，默认为pcm
	SampleRate  int           // 采样率，默认24000
	Channels    int           // 声道数，默认1
	Timeout     time.Duration // 请求超时时间

	// HTTP 客户端
	client *http.Client
}

// TTSRequest TTS请求结构体
type TTSRequest struct {
	Text      string `json:"text"`
	Character string `json:"character"`
}

type TTSRefRequest struct {
	Text      string   `json:"text"`
	RefAudios []string `json:"audio_paths"`
	Seed      int      `json:"seed"`
}

// init 函数注册GPT-SoVITS V3 TTS提供者
func init() {
	// 注册 GPT-SoVITS V3 TTS 提供者
	tts.Register([]string{"TTS_GPT_SOVITS_V2", constants.TtsTypeGptSovitsV2}, "GPT-SoVITS V2 TTS 语音合成服务", func(config map[string]interface{}) (tts.BaseTTSProvider, error) {
		provider := NewIndexStreamTTSProvider(config)
		return provider, nil
	})
}

// NewIndexStreamTTSProvider 创建IndexStreamTTSProvider
func NewIndexStreamTTSProvider(config map[string]interface{}) *IndexStreamTTSProvider {

	helper := util.NewConfigHelper(config)
	refAudios := helper.GetString("ref_audio")
	voice := helper.GetString("voice", "xiao_he")
	apiURL := helper.GetString("api_url")
	if apiURL == "" {
		apiURL = helper.GetString("url")
	}
	audioFormat := helper.GetString("audio_format", "pcm")
	sampleRate := helper.GetInt("sample_rate", 24000)
	channels := helper.GetInt("channels", 1)
	seed := helper.GetInt("seed", 8)
	timeout := helper.GetInt("timeout", 10)
	privateVoice := helper.GetString("private_voice")
	if privateVoice != "" {
		voice = privateVoice
	}

	log.Debugf("IndexStream TTS API URL: %s config: %v", apiURL, config)

	return &IndexStreamTTSProvider{
		RefAudios:   refAudios,
		Seed:        seed,
		Voice:       voice,
		APIURL:      apiURL,
		AudioFormat: audioFormat,
		SampleRate:  sampleRate,
		Channels:    channels,
		Timeout:     time.Duration(timeout) * time.Second,
		client: &http.Client{
			Timeout: time.Duration(timeout) * time.Second,
		},
	}
}

// TextToSpeech 一次性合成，返回Opus帧
func (p *IndexStreamTTSProvider) TextToSpeech(ctx context.Context, text string, sampleRate int, channels int, frameDuration int) ([][]byte, error) {
	startTs := time.Now().UnixMilli()
	log.Infof("IndexStream TTS开始合成文本: %s", text)

	var payload any
	if p.RefAudios != "" {
		payload = TTSRefRequest{
			Text:      text,
			RefAudios: strings.Split(p.RefAudios, ","),
			Seed:      p.Seed,
		}
	} else {
		payload = TTSRequest{
			Text:      text,
			Character: p.Voice,
		}
	}

	jsonData, err := json.Marshal(payload)
	if err != nil {
		return nil, fmt.Errorf("序列化请求失败: %v", err)
	}

	// 创建HTTP请求
	req, err := http.NewRequestWithContext(ctx, "POST", p.APIURL, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %v", err)
	}
	req.Header.Set("Content-Type", "application/json")

	// 发送请求
	resp, err := p.client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("TTS请求失败: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("TTS请求失败: %d, %s", resp.StatusCode, string(body))
	}

	// 读取所有音频数据
	pcmData, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取音频数据失败: %v", err)
	}

	// 转换PCM为Opus帧
	frames, err := p.convertPCMToOpusFrames(pcmData, frameDuration)
	if err != nil {
		return nil, fmt.Errorf("PCM转Opus失败: %v", err)
	}

	log.Infof("IndexStream TTS合成完成，耗时: %d ms，生成帧数: %d",
		time.Now().UnixMilli()-startTs, len(frames))

	return frames, nil
}

// TextToSpeechStream 流式合成，返回Opus帧chan
func (p *IndexStreamTTSProvider) TextToSpeechStream(ctx context.Context, text string, sampleRate int, channels int, frameDuration int) (chan []byte, error) {
	startTs := time.Now().UnixMilli()
	log.Infof("IndexStream TTS开始流式合成文本: %s", text)

	var payload any
	if p.RefAudios != "" {
		payload = TTSRefRequest{
			Text:      text,
			RefAudios: strings.Split(p.RefAudios, ","),
			Seed:      p.Seed,
		}
	} else {
		payload = TTSRequest{
			Text:      text,
			Character: p.Voice,
		}
	}

	jsonData, err := json.Marshal(payload)
	if err != nil {
		return nil, fmt.Errorf("序列化请求失败: %v", err)
	}

	// 创建HTTP请求
	req, err := http.NewRequestWithContext(ctx, "POST", p.APIURL, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %v", err)
	}
	req.Header.Set("Content-Type", "application/json")

	// 发送请求
	resp, err := p.client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("TTS请求失败: %v", err)
	}

	if resp.StatusCode != http.StatusOK {
		resp.Body.Close()
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("TTS请求失败: %d, %s", resp.StatusCode, string(body))
	}

	// 检查响应的Content-Type来确定音频格式
	contentType := resp.Header.Get("Content-Type")
	audioFormat := util.GetAudioFormatByMimeType(contentType)
	log.Debugf("IndexStream 响应Content-Type: %s, 解析音频格式: %s", contentType, audioFormat)

	// 如果无法从Content-Type确定格式，使用配置的格式
	if audioFormat == "mp3" && p.AudioFormat == "pcm" {
		audioFormat = "pcm"
	}

	outputChan := make(chan []byte, 100)
	pipeReader, pipeWriter := io.Pipe()

	// 处理HTTP响应流并写入pipe
	go func() {
		defer func() {
			pipeWriter.Close()
			resp.Body.Close()
			log.Infof("IndexStream TTS流式合成结束，耗时: %d ms", time.Now().UnixMilli()-startTs)
		}()

		buffer := make([]byte, 4096)
		for {
			select {
			case <-ctx.Done():
				log.Debugf("IndexStream TTS Stream context done, exit")
				return
			default:
				n, err := resp.Body.Read(buffer)
				if err != nil {
					if err == io.EOF {
						log.Debugf("IndexStream TTS Stream finished reading")
						return
					}
					log.Errorf("读取音频流失败: %v", err)
					return
				}

				if n == 0 {
					continue
				}

				// 将音频数据写入pipe
				_, err = pipeWriter.Write(buffer[:n])
				if err != nil {
					log.Errorf("写入音频数据到pipe失败: %v", err)
					return
				}
			}
		}
	}()

	// 启动音频解码
	go func() {
		// 创建音频解码器，根据检测到的格式设置
		audioDecoder, err := util.CreateAudioDecoder(ctx, pipeReader, outputChan, frameDuration, audioFormat)
		//audioDecoder = audioDecoder.WithFadeIn(true, 200)
		if err != nil {
			log.Errorf("IndexStream 音频解码器创建失败: %v", err)
			return
		}

		// 如果是PCM格式，设置格式参数
		if audioFormat == "pcm" {
			audioDecoder = audioDecoder.WithFormat(beep.Format{
				SampleRate:  beep.SampleRate(p.SampleRate),
				NumChannels: p.Channels,
				Precision:   2, // 16-bit
			})
		}

		if err := audioDecoder.Run(startTs); err != nil {
			log.Errorf("IndexStream 音频解码失败: %v", err)
		}
		log.Debugf("IndexStream 音频解码结束, 耗时: %d ms", time.Now().UnixMilli()-startTs)
	}()

	return outputChan, nil
}

// convertPCMToOpusFrames 将PCM数据转换为Opus帧
func (p *IndexStreamTTSProvider) convertPCMToOpusFrames(pcmData []byte, frameDuration int) ([][]byte, error) {
	frameBytes := p.SampleRate * p.Channels * frameDuration / 1000 * 2
	var frames [][]byte

	// 创建Opus编码器
	opusEncoder, err := util.NewOpusEncoder(p.SampleRate, p.Channels, frameDuration)
	if err != nil {
		return nil, fmt.Errorf("创建opus编码器失败: %v", err)
	}
	defer opusEncoder.Close()

	// 处理完整的帧
	for i := 0; i < len(pcmData); i += frameBytes {
		end := i + frameBytes
		if end > len(pcmData) {
			// 处理最后一个不完整的帧
			lastFrame := make([]byte, frameBytes)
			copy(lastFrame, pcmData[i:])

			opusFrame, err := opusEncoder.Encode(lastFrame)
			if err != nil {
				return nil, fmt.Errorf("opus编码失败: %v", err)
			}
			if opusFrame != nil {
				frames = append(frames, opusFrame)
			}
			break
		}

		frame := pcmData[i:end]
		opusFrame, err := opusEncoder.Encode(frame)
		if err != nil {
			return nil, fmt.Errorf("opus编码失败: %v", err)
		}
		if opusFrame != nil {
			frames = append(frames, opusFrame)
		}
	}

	return frames, nil
}

package types

import "time"

// ServerConfig represents the server configuration returned by /config/server-base
type ServerConfig struct {
	// Core server settings
	WebSocket      string `json:"websocket"`
	MCPEndpoint    string `json:"mcp_endpoint"`
	VoicePrint     string `json:"voice_print"`
	TimezoneOffset string `json:"timezone_offset"`
	VisionExplain  string `json:"vision_explain"`

	// Server connection details
	IP       string `json:"ip"`
	Port     int    `json:"port"`
	HTTPPort int    `json:"http_port"`

	// Authentication configuration
	Auth AuthConfig `json:"auth"`

	// Dynamic additional fields
	Additional map[string]interface{} `json:"-"` // For any extra fields not defined above
}

// AuthConfig represents authentication configuration
type AuthConfig struct {
	Enabled bool          `json:"enabled"`
	Tokens  []TokenConfig `json:"tokens"`
}

// TokenConfig represents individual token configuration
type TokenConfig struct {
	Token string `json:"token"`
	Name  string `json:"name"`
}

// AgentModelsRequest represents the request for /config/agent-models
type AgentModelsRequest struct {
	MacAddress     string            `json:"macAddress"`
	ClientID       string            `json:"clientId"`
	SelectedModule map[string]string `json:"selectedModule"`
}

// AgentModelsResponse represents the optimized agent model configuration response
type AgentModelsResponse struct {
	// 设备每天最大输出字数限制
	DeviceMaxOutputSize string `json:"device_max_output_size"`

	// 聊天记录配置 (0不记录 1仅记录文本 2记录文本和语音)
	ChatHistoryConf int `json:"chat_history_conf"`

	// 插件参数配置 (当意图模型不是 "Intent_nointent" 时存在)
	Plugins map[string]interface{} `json:"plugins,omitempty"`

	// MCP 接入点地址 (WebSocket 地址，如果配置了的话)
	McpEndpoint string `json:"mcp_endpoint,omitempty"`

	// 声纹配置
	Voiceprint *VoiceprintConfig `json:"voiceprint,omitempty"`

	// AI 模型配置 (通过 buildModuleConfig 方法构建)
	SelectedModule map[string]string `json:"selected_module"`

	Prompt string                            `json:"prompt,omitempty"`
	Memory map[string]map[string]interface{} `json:"Memory,omitempty"`
	// 具体字段取决于智能体配置的模型类型
	VAD    map[string]map[string]interface{} `json:"VAD,omitempty"`
	ASR    map[string]map[string]interface{} `json:"ASR,omitempty"`
	LLM    map[string]map[string]interface{} `json:"LLM,omitempty"`
	VLLM   map[string]map[string]interface{} `json:"VLLM,omitempty"`
	TTS    map[string]map[string]interface{} `json:"TTS,omitempty"`
	Intent map[string]map[string]interface{} `json:"Intent,omitempty"`
}

// VoiceprintConfig represents voice print configuration
type VoiceprintConfig struct {
	URL      string   `json:"url"`
	Speakers []string `json:"speakers"`
}

// ModelConfig represents AI model configuration
type ModelConfig struct {
	// 模型配置的具体字段取决于 buildModuleConfig 方法的实现
	// 通常包含模型ID、配置参数等
	ModelId string                 `json:"model_id,omitempty"`
	Config  map[string]interface{} `json:"config,omitempty"`
}

// AgentModelsConfig 为了向后兼容保留的类型别名
type AgentModelsConfig = AgentModelsResponse

// FunctionInfo represents function configuration information
type FunctionInfo struct {
	ID          string                 `json:"id"`
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	Parameters  map[string]interface{} `json:"parameters"`
	Enabled     bool                   `json:"enabled"`
}

// ConfigCache represents cached configuration with TTL
type ConfigCache struct {
	ServerConfig  *ServerConfig                 `json:"server_config"`
	AgentConfigs  map[string]*AgentModelsConfig `json:"agent_configs"` // keyed by device identifier
	LastUpdated   time.Time                     `json:"last_updated"`
	TTL           time.Duration                 `json:"ttl"`
	RequestCounts map[string]int                `json:"request_counts"` // API endpoint request counts
	ErrorCounts   map[string]int                `json:"error_counts"`   // API endpoint error counts
}

// IsExpired checks if the cached configuration has expired
func (c *ConfigCache) IsExpired() bool {
	if c.LastUpdated.IsZero() {
		return true
	}
	return time.Since(c.LastUpdated) > c.TTL
}

// GetDeviceKey generates a unique key for device configuration caching
func GetDeviceKey(macAddress, clientID string, modules map[string]string) string {
	key := macAddress + ":" + clientID
	if len(modules) > 0 {
		key += ":"
		for k, v := range modules {
			key += k + "=" + v + ","
		}
	}
	return key
}

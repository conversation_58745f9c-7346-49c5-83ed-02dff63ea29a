package manager_api

import (
	"context"
	"fmt"
	"sync"
	"time"

	"xiaozhi-esp32-server-golang/internal/manager_api/types"
	"xiaozhi-esp32-server-golang/logger"
)

// Service implements the ManagerAPIService interface
type Service struct {
	client            ManagerAPIClient
	config            *Config
	memoryCache       *types.MemoryCache
	chatHistoryBuffer *types.ChatHistoryBuffer
	configCache       *types.ConfigCache
	healthTicker      *time.Ticker
	bufferTicker      *time.Ticker
	isRunning         bool
	stopChan          chan struct{}
	wg                sync.WaitGroup
	mutex             sync.RWMutex
}

// NewService creates a new manager-api service
func NewService(config *Config) (*Service, error) {
	if err := config.Validate(); err != nil {
		return nil, err
	}

	client, err := NewHTTPClient(config)
	if err != nil {
		return nil, err
	}

	return &Service{
		client:      client,
		config:      config,
		memoryCache: types.NewMemoryCache(1000, time.Duration(config.CacheTTLSeconds)*time.Second),
		chatHistoryBuffer: types.NewChatHistoryBuffer(
			config.BatchSize,
			time.Duration(config.FlushIntervalSeconds)*time.Second,
		),
		configCache: &types.ConfigCache{
			AgentConfigs:  make(map[string]*types.AgentModelsConfig),
			RequestCounts: make(map[string]int),
			ErrorCounts:   make(map[string]int),
			TTL:           time.Duration(config.CacheTTLSeconds) * time.Second,
		},
		stopChan: make(chan struct{}),
	}, nil
}

// Start initializes the service and starts background processes
func (s *Service) Start(ctx context.Context) error {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	if s.isRunning {
		return fmt.Errorf("service is already running")
	}

	if !s.config.Enabled {
		logger.Info("Manager-API service is disabled")
		return nil
	}

	logger.Info("Starting Manager-API service...")

	// 执行初始健康检查
	if err := s.client.HealthCheck(ctx); err != nil {
		logger.Warnf("Initial health check failed: %v", err)
	} else {
		logger.Info("Initial health check passed")
	}

	// Start background workers
	s.startHealthChecker()
	s.startChatHistoryFlusher()

	s.isRunning = true
	logger.Info("Manager-API service started successfully")
	return nil
}

// Stop gracefully shuts down the service
func (s *Service) Stop(ctx context.Context) error {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	if !s.isRunning {
		return nil
	}

	logger.Info("Stopping Manager-API service...")

	// Stop tickers
	if s.healthTicker != nil {
		s.healthTicker.Stop()
	}
	if s.bufferTicker != nil {
		s.bufferTicker.Stop()
	}

	// Signal workers to stop
	close(s.stopChan)

	// Flush remaining chat history
	if err := s.flushChatHistory(ctx); err != nil {
		logger.Errorf("Error flushing chat history during shutdown: %v", err)
	}

	// Wait for workers to finish with timeout
	done := make(chan struct{})
	go func() {
		s.wg.Wait()
		close(done)
	}()

	select {
	case <-done:
		logger.Info("All workers stopped gracefully")
	case <-time.After(10 * time.Second):
		logger.Warn("Timeout waiting for workers to stop")
	}

	s.isRunning = false
	logger.Info("Manager-API service stopped")

	return nil
}

// GetServerConfig retrieves and caches server configuration
func (s *Service) GetServerConfig(ctx context.Context) (*types.ServerConfig, error) {
	if !s.config.Enabled {
		return nil, ErrServiceUnavailable{Service: "manager-api", Reason: "service disabled"}
	}

	// Check cache first
	s.mutex.RLock()
	if s.configCache.ServerConfig != nil && !s.configCache.IsExpired() {
		s.mutex.RUnlock()
		return s.configCache.ServerConfig, nil
	}
	s.mutex.RUnlock()

	// Fetch from API
	config, err := s.client.GetServerConfig(ctx)
	if err != nil {
		return nil, err
	}

	// Update cache
	s.mutex.Lock()
	s.configCache.ServerConfig = config
	s.configCache.LastUpdated = time.Now()
	s.mutex.Unlock()

	logger.Debug("Server configuration retrieved and cached")
	return config, nil
}

// GetDeviceConfig retrieves device-specific configuration
func (s *Service) GetDeviceConfig(ctx context.Context, macAddress, clientID string, modules map[string]string) (*types.AgentModelsConfig, error) {
	if !s.config.Enabled {
		return nil, ErrServiceUnavailable{Service: "manager-api", Reason: "service disabled"}
	}

	cacheKey := types.GetDeviceKey(macAddress, clientID, modules)

	// Check cache first
	s.mutex.RLock()
	if cachedConfig, exists := s.configCache.AgentConfigs[cacheKey]; exists && !s.configCache.IsExpired() {
		s.mutex.RUnlock()
		return cachedConfig, nil
	}
	s.mutex.RUnlock()

	// Fetch from API
	req := &types.AgentModelsRequest{
		MacAddress:     macAddress,
		ClientID:       clientID,
		SelectedModule: modules,
	}

	config, err := s.client.GetAgentModels(ctx, req)
	if err != nil {
		return nil, err
	}
	if config == nil {
		return nil, fmt.Errorf("config is nil")
	}
	
	logger.Debugf("GetDeviceConfig config: %+v", config)

	// Update cache
	s.mutex.Lock()
	s.configCache.AgentConfigs[cacheKey] = config
	s.configCache.LastUpdated = time.Now()
	s.mutex.Unlock()

	logger.Debugf("Device configuration retrieved and cached for %s", macAddress)
	return config, nil
}

// RefreshConfig forces a refresh of cached configurations
func (s *Service) RefreshConfig(ctx context.Context) error {
	if !s.config.Enabled {
		return ErrServiceUnavailable{Service: "manager-api", Reason: "service disabled"}
	}

	s.mutex.Lock()
	s.configCache.ServerConfig = nil
	s.configCache.AgentConfigs = make(map[string]*types.AgentModelsConfig)
	s.configCache.LastUpdated = time.Time{}
	s.mutex.Unlock()

	logger.Info("Configuration cache refreshed")
	return nil
}

// InvalidateCache clears all cached configurations
func (s *Service) InvalidateCache() {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	s.configCache.ServerConfig = nil
	s.configCache.AgentConfigs = make(map[string]*types.AgentModelsConfig)
	s.configCache.LastUpdated = time.Time{}

	// Clear memory cache
	s.memoryCache.Clear()

	logger.Info("All caches invalidated")
}

// SaveDeviceMemory saves conversation summary memory for a device
func (s *Service) SaveDeviceMemory(ctx context.Context, macAddress, summaryMemory string) error {
	if !s.config.Enabled {
		return ErrServiceUnavailable{Service: "manager-api", Reason: "service disabled"}
	}

	req := &types.MemoryRequest{
		SummaryMemory: summaryMemory,
	}

	// Save to API
	err := s.client.SaveMemory(ctx, macAddress, req)
	if err != nil {
		logger.Errorf("Failed to save memory for device %s: %v", macAddress, err)
		return err
	}

	// Update local cache
	memory := &types.DeviceMemory{
		MacAddress:    macAddress,
		SummaryMemory: summaryMemory,
		LastUpdated:   time.Now(),
		Size:          len(summaryMemory),
	}
	s.memoryCache.Set(macAddress, memory)

	logger.Debugf("Memory saved for device %s", macAddress)
	return nil
}

// GetDeviceMemory retrieves stored memory for a device (from cache)
func (s *Service) GetDeviceMemory(ctx context.Context, macAddress string) (string, error) {
	if !s.config.Enabled {
		return "", ErrServiceUnavailable{Service: "manager-api", Reason: "service disabled"}
	}

	memory, exists := s.memoryCache.Get(macAddress)
	if !exists {
		return "", fmt.Errorf("memory not found for device %s", macAddress)
	}

	return memory.SummaryMemory, nil
}

// ReportChat reports a single chat message with optional audio data
func (s *Service) ReportChat(ctx context.Context, macAddress, sessionID, chatType, content string, audioData []byte) error {
	if !s.config.Enabled {
		return ErrServiceUnavailable{Service: "manager-api", Reason: "service disabled"}
	}

	req := &types.ChatHistoryRequest{
		MacAddress: macAddress,
		SessionID:  sessionID,
		ChatType:   chatType,
		Content:    content,
		ReportTime: time.Now(),
	}

	// Encode audio data if provided
	if len(audioData) > 0 {
		audioDataObj := &types.AudioData{
			Data:      audioData,
			Size:      len(audioData),
			Timestamp: time.Now(),
		}
		req.AudioBase64 = audioDataObj.EncodeToBase64()
	}

	// Add to buffer for batch processing
	shouldFlush := s.chatHistoryBuffer.Add(*req)

	// Immediate flush if buffer is full
	if shouldFlush {
		return s.flushChatHistory(ctx)
	}

	return nil
}

// ReportBatchChat reports multiple chat messages in batch
func (s *Service) ReportBatchChat(ctx context.Context, requests []*types.ChatHistoryRequest) error {
	if !s.config.Enabled {
		return ErrServiceUnavailable{Service: "manager-api", Reason: "service disabled"}
	}

	if len(requests) == 0 {
		return nil
	}

	var lastErr error
	successCount := 0

	for _, req := range requests {
		if err := s.client.ReportChatHistory(ctx, req); err != nil {
			logger.Errorf("Failed to report chat history for %s: %v", req.MacAddress, err)
			lastErr = err
		} else {
			successCount++
		}
	}

	logger.Infof("Batch chat report completed: %d/%d successful", successCount, len(requests))

	if successCount == 0 && lastErr != nil {
		return lastErr
	}

	return nil
}

// IsHealthy returns current health status
func (s *Service) IsHealthy() bool {
	if !s.config.Enabled {
		return true // Consider disabled service as healthy
	}
	if clientHealthy, ok := s.client.(interface{ IsHealthy() bool }); ok {
		return clientHealthy.IsHealthy()
	}
	return true
}

// startHealthChecker starts the background health checker
func (s *Service) startHealthChecker() {
	s.healthTicker = time.NewTicker(s.config.HealthCheckInterval)

	s.wg.Add(1)
	go func() {
		defer s.wg.Done()
		defer s.healthTicker.Stop()

		for {
			select {
			case <-s.healthTicker.C:
				ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
				err := s.client.HealthCheck(ctx)
				cancel()

				if err != nil {
					logger.Warnf("Health check failed: %v", err)
				}

			case <-s.stopChan:
				return
			}
		}
	}()

	logger.Debug("Health checker started")
}

// startChatHistoryFlusher starts the background chat history buffer flusher
func (s *Service) startChatHistoryFlusher() {
	flushInterval := time.Duration(s.config.FlushIntervalSeconds) * time.Second
	s.bufferTicker = time.NewTicker(flushInterval)

	s.wg.Add(1)
	go func() {
		defer s.wg.Done()
		defer s.bufferTicker.Stop()

		for {
			select {
			case <-s.bufferTicker.C:
				if s.chatHistoryBuffer.ShouldFlush() {
					ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
					if err := s.flushChatHistory(ctx); err != nil {
						logger.Errorf("Error flushing chat history: %v", err)
					}
					cancel()
				}

			case <-s.stopChan:
				return
			}
		}
	}()

	logger.Debug("Chat history flusher started")
}

// flushChatHistory flushes the chat history buffer
func (s *Service) flushChatHistory(ctx context.Context) error {
	messages := s.chatHistoryBuffer.GetMessages()
	if len(messages) == 0 {
		return nil
	}

	logger.Debugf("Flushing %d chat history messages", len(messages))

	// Convert to request pointers
	requests := make([]*types.ChatHistoryRequest, len(messages))
	for i := range messages {
		requests[i] = &messages[i]
	}

	return s.ReportBatchChat(ctx, requests)
}
